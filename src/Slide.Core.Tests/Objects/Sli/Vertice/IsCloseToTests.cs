using Slide.Core.Objects.Sli;

namespace Slide.Core.Tests.Objects.Sli.Vertice;

[Trait("Vertice", "IsCloseTo")]
public class IsCloseToTests
{
    private readonly Faker _faker = new();
    private const double Tolerance = Core.Objects.Sli.Vertice.ProximityTolerance;

    [Fact(DisplayName = "When other vertex is null, should return false")]
    public void WhenOtherVertexIsNull_ShouldReturnFalse()
    {
        var vertex = new Core.Objects.Sli.Vertice(_faker.Random.Double(), _faker.Random.Double());

        var result = vertex.IsCloseTo(null);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices are exactly the same, should return true")]
    public void WhenVerticesAreExactlyTheSame_ShouldReturnTrue()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var y = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Core.Objects.Sli.Vertice(x, y);
        var vertex2 = new Core.Objects.Sli.Vertice(x, y);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are at exactly tolerance distance, should return true")]
    public void WhenVerticesAreAtExactlyToleranceDistance_ShouldReturnTrue()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(0, 0);
        var vertex2 = new Core.Objects.Sli.Vertice(Tolerance, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are within tolerance threshold, should return true")]
    public void WhenVerticesAreWithinToleranceThreshold_ShouldReturnTrue()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(0, 0);
        var vertex2 = new Core.Objects.Sli.Vertice(0.0005, 0.0005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are just outside tolerance threshold, should return false")]
    public void WhenVerticesAreJustOutsideToleranceThreshold_ShouldReturnFalse()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(0, 0);
        var vertex2 = new Core.Objects.Sli.Vertice(0.002, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have zero coordinates, should return true")]
    public void WhenVerticesHaveZeroCoordinates_ShouldReturnTrue()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(0, 0);
        var vertex2 = new Core.Objects.Sli.Vertice(0, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have negative coordinates within tolerance, should return true")]
    public void WhenVerticesHaveNegativeCoordinatesWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(-10.5, -20.3);
        var vertex2 = new Core.Objects.Sli.Vertice(-10.5005, -20.3005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have negative coordinates outside tolerance, should return false")]
    public void WhenVerticesHaveNegativeCoordinatesOutsideTolerance_ShouldReturnFalse()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(-10.5, -20.3);
        var vertex2 = new Core.Objects.Sli.Vertice(-10.502, -20.3);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have large positive coordinates within tolerance, should return true")]
    public void WhenVerticesHaveLargePositiveCoordinatesWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(1000000.5, 2000000.3);
        var vertex2 = new Core.Objects.Sli.Vertice(1000000.5005, 2000000.3005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have large positive coordinates outside tolerance, should return false")]
    public void WhenVerticesHaveLargePositiveCoordinatesOutsideTolerance_ShouldReturnFalse()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(1000000.5, 2000000.3);
        var vertex2 = new Core.Objects.Sli.Vertice(1000000.502, 2000000.3);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When distance is calculated using Euclidean formula within tolerance, should return true")]
    public void WhenDistanceIsCalculatedUsingEuclideanFormulaWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(0, 0);
        // Distance = sqrt((0.0006)² + (0.0008)²) = sqrt(0.00000036 + 0.00000064) = sqrt(0.000001) = 0.001
        var vertex2 = new Core.Objects.Sli.Vertice(0.0006, 0.0008);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When distance is calculated using Euclidean formula outside tolerance, should return false")]
    public void WhenDistanceIsCalculatedUsingEuclideanFormulaOutsideTolerance_ShouldReturnFalse()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(0, 0);
        // Distance = sqrt((0.0008)² + (0.0008)²) = sqrt(0.00000064 + 0.00000064) = sqrt(0.00000128) ≈ 0.00113 > 0.001
        var vertex2 = new Core.Objects.Sli.Vertice(0.0008, 0.0008);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices differ only in X coordinate within tolerance, should return true")]
    public void WhenVerticesDifferOnlyInXCoordinateWithinTolerance_ShouldReturnTrue()
    {
        var y = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Core.Objects.Sli.Vertice(100, y);
        var vertex2 = new Core.Objects.Sli.Vertice(100.0005, y);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices differ only in Y coordinate within tolerance, should return true")]
    public void WhenVerticesDifferOnlyInYCoordinateWithinTolerance_ShouldReturnTrue()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Core.Objects.Sli.Vertice(x, 200);
        var vertex2 = new Core.Objects.Sli.Vertice(x, 200.0005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices differ only in X coordinate outside tolerance, should return false")]
    public void WhenVerticesDifferOnlyInXCoordinateOutsideTolerance_ShouldReturnFalse()
    {
        var y = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Core.Objects.Sli.Vertice(100, y);
        var vertex2 = new Core.Objects.Sli.Vertice(100.002, y);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices differ only in Y coordinate outside tolerance, should return false")]
    public void WhenVerticesDifferOnlyInYCoordinateOutsideTolerance_ShouldReturnFalse()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Core.Objects.Sli.Vertice(x, 200);
        var vertex2 = new Core.Objects.Sli.Vertice(x, 200.002);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have very small coordinate differences within tolerance, should return true")]
    public void WhenVerticesHaveVerySmallCoordinateDifferencesWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(0, 0);
        var vertex2 = new Core.Objects.Sli.Vertice(0.0001, 0.0001);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When comparing vertex with itself, should return true")]
    public void WhenComparingVertexWithItself_ShouldReturnTrue()
    {
        var vertex = new Core.Objects.Sli.Vertice(_faker.Random.Double(), _faker.Random.Double());

        var result = vertex.IsCloseTo(vertex);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have mixed positive and negative coordinates within tolerance, should return true")]
    public void WhenVerticesHaveMixedPositiveAndNegativeCoordinatesWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Core.Objects.Sli.Vertice(-5.5, 10.3);
        var vertex2 = new Core.Objects.Sli.Vertice(-5.5005, 10.3005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are just beyond boundary of tolerance using random coordinates, should return false")]
    public void WhenVerticesAreJustBeyondBoundaryOfToleranceUsingRandomCoordinates_ShouldReturnFalse()
    {
        var x1 = _faker.Random.Double(-1000, 1000);
        var y1 = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Core.Objects.Sli.Vertice(x1, y1);
        
        // Create a vertex just beyond tolerance distance
        var vertex2 = new Core.Objects.Sli.Vertice(x1 + Tolerance + 0.0001, y1);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }
}
