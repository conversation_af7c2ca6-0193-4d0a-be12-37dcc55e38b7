using Geometry.Core;
using Slide.Core;
using Slide.Core.Objects.Sli;
using TriangleNet.Meshing;

namespace Workflow.Extensions;

public static class SliFileExtensions
{
    public static SliFile FromDxf(
        this SliFile sliFile,
        List<Vertice> externalVertices,
        List<(Vertice, int)> materialVertices,
        IMesh mesh)
    {
        sliFile.AddVertices(
            externalVertices.Select(x => new PointD(x.X, x.Y)).ToList());
        
        sliFile.AddVertices(
            materialVertices.Select(x => new PointD(x.Item1.X, x.Item1.Y))
                .ToList());

        foreach (var triangle in mesh.Triangles)
        {
            var vertice1 = triangle.GetVertex(0);
            var vertice2 = triangle.GetVertex(1);
            var vertice3 = triangle.GetVertex(2);

            var sliVertice1 = sliFile.Vertices.FirstOrDefault(x =>
                x.X == vertice1.X && x.Y == vertice1.Y);

            if (sliVertice1 == null)
            {
                var newVertice = new PointD(vertice1.X, vertice1.Y);
                sliVertice1 = sliFile.AddVertice(newVertice);
            }

            var sliVertice2 = sliFile.Vertices.FirstOrDefault(x =>
                x.X == vertice2.X && x.Y == vertice2.Y);

            if (sliVertice2 == null)
            {
                var newVertice = new PointD(vertice2.X, vertice2.Y);
                sliVertice2 = sliFile.AddVertice(newVertice);
            }

            var sliVertice3 = sliFile.Vertices.FirstOrDefault(x =>
                x.X == vertice3.X && x.Y == vertice3.Y);

            if (sliVertice3 == null)
            {
                var newVertice = new PointD(vertice3.X, vertice3.Y);
                sliVertice3 = sliFile.AddVertice(newVertice);
            }

            sliFile.AddCell(
                new()
                {
                    Vertice1 = (int)sliVertice1.Index,
                    Vertice2 = (int)sliVertice2.Index,
                    Vertice3 = (int)sliVertice3.Index,
                    Material = "soil1"
                });
        }

        var externalVerticesIndex = externalVertices
            .Select(x => (int)sliFile.Vertices
                .First(v => v.X == x.X && v.Y == x.Y)
                .Index)
            .Distinct()
            .ToList();

        sliFile.AddExterior(externalVerticesIndex);

        var minX = externalVertices.Min(x => x.X);
        var maxX = externalVertices.Max(x => x.X);

        var leftmostHighestVertex = externalVertices
            .Where(x => x.X == minX)
            .OrderByDescending(x => x.Y)
            .First();
        
        var rightmostHighestVertex = externalVertices
            .Where(x => x.X == maxX)
            .OrderByDescending(x => x.Y)
            .First();

        var minYAtCrests = Math.Min(leftmostHighestVertex.Y, rightmostHighestVertex.Y);

        var upperVertices = externalVertices
            .Where(vertex =>
            {
                var isAnEdge = 
                    (vertex.X == leftmostHighestVertex.X && vertex.Y == leftmostHighestVertex.Y) ||
                    (vertex.X == rightmostHighestVertex.X && vertex.Y == rightmostHighestVertex.Y);

                var isAnIntermediate = 
                    vertex.X > leftmostHighestVertex.X &&
                    vertex.X < rightmostHighestVertex.X &&
                    vertex.Y >= minYAtCrests;
                
                return isAnIntermediate || isAnEdge;
            })
            .OrderBy(x => x.X)
            .ToList();

        var upperVerticesIndex = upperVertices
            .Select(x => (int)sliFile.Vertices
                .First(v => v.X == x.X && v.Y == x.Y)
                .Index)
            .Distinct()
            .ToList();

        sliFile.AddSlope(upperVerticesIndex);

        upperVertices = upperVertices.OrderBy(x => x.X).ToList();

        var upperVerticeWithMinX = upperVertices.First();
        var upperVerticeWithMaxX = upperVertices.Last();

        sliFile.AddSlopeLimits(upperVerticeWithMinX, upperVerticeWithMaxX);

        var lastExternalVertice = externalVertices.Last();

        sliFile.AddGeometryInfos(
            externalVertices.Select(x => new GeometryInfo()
                {
                    X = x.X,
                    Y = x.Y,
                    MaterialType = 0,
                    EndFlag = x.X == lastExternalVertice.X &&
                              x.Y == lastExternalVertice.Y &&
                              x.Index == lastExternalVertice.Index
                        ? true
                        : false,
                })
                .ToList());

        foreach (var group in materialVertices.GroupBy(x => x.Item2))
        {
            for (int i = 0; i < group.Count(); i++)
            {
                var element1 = group.ElementAtOrDefault(i).Item1;
                var element2 = group.ElementAtOrDefault(i + 1).Item1;

                if (element1 != null && element2 != null)
                {
                    sliFile.AddGeometryInfo(
                        new()
                        {
                            X = element1.X,
                            Y = element1.Y,
                            MaterialType = 3,
                            EndFlag = false
                        });
                }
                else
                {
                    sliFile.AddGeometryInfo(
                        new()
                        {
                            X = element1.X,
                            Y = element1.Y,
                            MaterialType = 3,
                            EndFlag = true
                        });
                }
            }
        }

        sliFile.AddMaterialProperties(
            new()
            {
                Name = "Support 1",
                Red = 0,
                Green = 0,
                Blue = 255,
                Guid = Guid.NewGuid()
            });

        sliFile.AddMaterialProperties(
            new()
            {
                Name = "Support 2",
                Red = 0,
                Green = 255,
                Blue = 0,
                Guid = Guid.NewGuid()
            });

        return sliFile;
    }
}
