using Slide.Core.Objects.Sli;

namespace Workflow.Extensions;

public static class VertexExtensions
{
    /// <summary>
    /// Determines if this vertex is close to another vertex within a specified tolerance distance.
    /// Uses Euclidean distance calculation to compare the two vertices.
    /// </summary>
    /// <param name="other">The other vertex to compare against.</param>
    /// <returns>
    /// True if the distance between the vertices is less than or equal to the tolerance; otherwise, false.
    /// Returns false if the other vertex is null.
    /// </returns>
    public static bool IsCloseTo(
        this TriangleNet.Geometry.Vertex vertex, 
        TriangleNet.Geometry.Vertex other)
    {
        const double tolerance = 0.001;

        if (other == null)
        {
            return false;
        }

        // Calculate Euclidean distance between the two vertices
        var distance = Math.Sqrt(Math.Pow(vertex.X - other.X, 2) + Math.Pow(vertex.Y - other.Y, 2));

        return distance <= tolerance;
    }
}
