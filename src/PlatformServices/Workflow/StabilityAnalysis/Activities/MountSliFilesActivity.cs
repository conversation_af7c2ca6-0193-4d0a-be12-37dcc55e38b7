using Application.Apis.Clients;
using Application.Apis.Clients.Response.Instrument;
using Dxf.Core.Extensions;
using Elsa;
using Elsa.ActivityResults;
using Elsa.Services.Models;
using Geometry.Core;
using Serilog;
using Workflow.Extensions;
using Workflow.StabilityAnalysis.Classes;
using Workflow.StabilityAnalysis.Patterns.Builder;
using static Workflow.Constants.StabilityAnalysisWorkFlow;
using Activity = Elsa.Services.Activity;
using Helper = Dxf.Core.Helper;

namespace Workflow.StabilityAnalysis.Activities
{
    public sealed class MountSliFilesActivity : Activity
    {
        private readonly IClientsApiService _clientsApiService;
        private const double MaximumDistanceBetweenInstruments = 2.5;

        public MountSliFilesActivity(IClientsApiService clientsApiService)
        {
            _clientsApiService = clientsApiService;
        }

        protected override async ValueTask<IActivityExecutionResult> OnExecuteAsync(ActivityExecutionContext context)
        {
            try
            {
                var structureInfo = context.GetTransientVariable<StructureInfo>(Variables.StructureInfo);

                var files = new List<SliDetailAggregator>();

                foreach (var sectionInfo in structureInfo.Sections)
                {
                    if (sectionInfo.SectionReviewData == null || sectionInfo.SectionReviewData.SectionReview == null)
                    {
                        Log.Warning("Não foi possível prosseguir com a análise de estabilidade da seção {0} pois essa seção não tem revisões.",sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade da seção {sectionInfo.SectionName} pois essa seção não tem revisões.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    if (string.IsNullOrEmpty(sectionInfo.SectionReviewData.Sli?.Base64) || string.IsNullOrEmpty(sectionInfo.SectionReviewData.Dxf?.Base64))
                    {
                        Log.Warning("Não conseguimos continuar com a análise de estabilidade da seção {0} porque ela está sem os arquivos básicos necessários para iniciar o processo.", sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não conseguimos continuar com a análise de estabilidade da seção {sectionInfo.SectionName} porque ela está sem os arquivos básicos necessários para iniciar o processo.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var dxfFile = sectionInfo.SectionReviewData.Dxf.LoadDxfFile();

                    var fillingEntities = dxfFile.GetFillingEntities();

                    if (!fillingEntities.Any())
                    {
                        Log.Warning("O DXF da seção {0} não possui entidades de preenchimento (SOLID ou HATCH).", sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"O DXF da seção {sectionInfo.SectionName} não possui entidades de preenchimento (SOLID ou HATCH).");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var materialSearchIdentifiers = fillingEntities.GetMaterialSearchIdentifiers();

                    if (!materialSearchIdentifiers.Any())
                    {
                        Log.Warning("O DXF da seção {0} não possui camadas com nome que represente o ID do material estático.", sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"O DXF da seção {sectionInfo.SectionName} não possui camadas com nome que represente o ID do material estático.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var materials = await _clientsApiService
                       .GetStaticMaterialsBySearchIds(materialSearchIdentifiers, structureInfo.StaticMaterialReviewDate);

                    if (materials == null || !materials.Any())
                    {
                        Log.Warning("Não foi possível prosseguir com a análise de estabilidade da seção {0} pois não foi possível encontrar os materiais estáticos.", sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"Não foi possível prosseguir com a análise de estabilidade da seção {sectionInfo.SectionName} pois não foi possível encontrar os materiais estáticos.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var externalPoints = dxfFile.GetExternalPoints();

                    if (!externalPoints.Any())
                    {
                        Log.Warning("O DXF da seção {0} não possui pontos externos.",sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"O DXF da seção {sectionInfo.SectionName} não possui pontos externos.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var instrumentPoints = new InstrumentPoints();

                    if (sectionInfo.Instruments.Any() && !sectionInfo.SectionReviewData.SectionReview.DxfHasWaterline)
                    {
                        var instrumentInfos = await GetInstrumentInfos(sectionInfo);

                        instrumentPoints = dxfFile.GetInstrumentPoints(sectionInfo, instrumentInfos);

                        if (!instrumentPoints.Piezometers.Any() && !instrumentPoints.WaterLevelIndicators.Any())
                        {
                            Log.Warning("A seção {0} não possui INAs e PZs.", sectionInfo.SectionName);
                            context.SetTransientVariable(Variables.ErrorMessage, $"A seção {sectionInfo.SectionName} não possui INAs e PZs.");
                            return Outcome(OutcomeNames.Cancel);
                        }
                    }
                    else if (sectionInfo.SectionReviewData.SectionReview.DxfHasWaterline)
                    {
                        instrumentPoints = dxfFile.GetInstrumentPoints(sectionInfo);

                        if (sectionInfo.Instruments.Any())
                        {
                            var instrumentInfos = await GetInstrumentInfos(sectionInfo);

                            var points2 = dxfFile.GetInstrumentPoints(sectionInfo, instrumentInfos);

                            foreach (var point2 in points2.Piezometers.Concat(points2.WaterLevelIndicators))
                            {
                                var combinedPoints = instrumentPoints.Piezometers.Concat(instrumentPoints.WaterLevelIndicators).ToList();

                                var closestPoint = new Instrument();
                                var closestDistance = double.MaxValue;

                                foreach (var point in combinedPoints)
                                {
                                    var distance = point.PointD.XDistance(point2.PointD);
                                    if (distance < closestDistance)
                                    {
                                        closestDistance = distance;
                                        closestPoint = point;
                                    }
                                }

                                if (closestDistance <= MaximumDistanceBetweenInstruments)
                                {
                                    closestPoint.PointD = new PointD(closestPoint.PointD.X, point2.PointD.Y);
                                    closestPoint.InstrumentInfo = point2.InstrumentInfo;
                                }
                                else if (closestPoint != null)  
                                {
                                    var elementIndex = instrumentPoints.WaterLevelIndicators.IndexOf(closestPoint);
                                    var index = closestPoint.PointD.X < point2.PointD.X ? elementIndex + 1 : elementIndex - 1;

                                    if (closestPoint.InstrumentInfo != null || index == 0)
                                    {
                                        index++;
                                    }

                                    instrumentPoints.WaterLevelIndicators.Insert(index, point2);
                                }
                                else
                                {
                                    instrumentPoints.WaterLevelIndicators.Add(point2);
                                }
                            }
                        }
                    }
                    else
                    {
                        Log.Warning("A seção {0} não possui instrumentos e não possui linha freática no DXF.", sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"A seção {sectionInfo.SectionName} não possui instrumentos e não possui linha freática no DXF.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var fixedPoints = dxfFile.GetFixedPoints();

                    var beachLength = sectionInfo.BeachLength;

                    var circularParameters = structureInfo.Slide2Configuration?.CircularParameters;
                    var nonCircularParameters = structureInfo.Slide2Configuration?.NonCircularParameters;

                    if (circularParameters == null && nonCircularParameters == null)
                    {
                        Log.Warning("A estrutura {0} não possui parâmetros circulares ou não circulares.", structureInfo.StructureName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"A estrutura {structureInfo.StructureName} não possui parâmetros circulares ou não circulares.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    if (circularParameters != null && !circularParameters.CalculationMethods.Any())
                    {
                        Log.Warning("A estrutura {0} não possui parâmetros circulares.", structureInfo.StructureName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"A estrutura {structureInfo.StructureName} não possui parâmetros circulares.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    if (nonCircularParameters != null && !nonCircularParameters.CalculationMethods.Any())
                    {
                        Log.Warning("A estrutura {0} não possui parâmetros não-circulares.", structureInfo.StructureName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"A estrutura {structureInfo.StructureName} não possui parâmetros não-circulares.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var polygons = Helper.GetPolygons(fillingEntities);

                    if (!polygons.Any() || !polygons.Select(x => x.Layer).Any())
                    {
                        Log.Warning("O DXF da seção {0} não possui polígonos, que definem as regiões dos materiais e que são gerados a partir de entidades do tipo HATCH ou SOLID.", sectionInfo.SectionName);
                        context.SetTransientVariable(Variables.ErrorMessage, $"O DXF da seção {sectionInfo.SectionName} não possui polígonos, que definem as regiões dos materiais e que são gerados a partir de entidades do tipo HATCH ou SOLID.");
                        return Outcome(OutcomeNames.Cancel);
                    }

                    var useBeachLength = sectionInfo.ShouldUseBeachLength; 
                    var slopeLimits = dxfFile.GetSlopeLimits();

                    var sliFiles = new SliFileBuilder()
                        .Load(sectionInfo.SectionReviewData.Sli.Base64, structureInfo, materials)
                        .SetParameters(circularParameters, nonCircularParameters, structureInfo.SafetyFactorTarget, sectionInfo, dxfFile)
                        .SetMaterialProperties(materials)
                        .SetCellMaterials(materials, polygons)
                        .SetWaterLine(externalPoints, fixedPoints, instrumentPoints, sectionInfo.SectionReviewData.SectionReview, useBeachLength ? beachLength : null, sectionInfo.UpstreamLinimetricRulerQuota, sectionInfo.DownstreamLinimetricRulerQuota, sectionInfo.SectionReviewData.SectionReview.DxfHasWaterline)
                        .SetSeismicLoad(structureInfo)
                        .SetSlopeLimits(slopeLimits, externalPoints)
                        .Build();

                    foreach (var sliFile in sliFiles)
                    {
                        files.Add(new SliDetailAggregator
                        {
                            SliFile = sliFile.Item1,
                            SliFileType = sliFile.Item2,
                            DxfFile = dxfFile,
                            Section = sectionInfo,
                            Instruments = instrumentPoints,
                            Structure = structureInfo
                        });
                    }
                }

                context.SetTransientVariable(Variables.SliFiles, files);

                return Done();
            }
            catch (Exception e)
            {
                Log.Error(e, "Error in MountSliFilesActivity");
                context.JournalData.Add(Variables.ErrorMessage, $"Não conseguimos continuar com a análise de estabilidade porque houve um erro ao montar os arquivos necessários: {e.Message}");
                context.SetTransientVariable(Variables.ErrorMessage, $"Não conseguimos continuar com a análise de estabilidade porque houve um erro ao montar os arquivos necessários: {e.Message}");
                return Outcome(OutcomeNames.Cancel);
            }
        }

        private async Task<List<GetInstrumentByIdResponse>> GetInstrumentInfos(
            SectionInfo section)
        {
            var ids = section.Instruments
                .Select(instrument => instrument.InstrumentId)
                .Distinct();
            
            return (await _clientsApiService.GetInstrumentsByIds(ids))?
                .ToList();
        }
    }
}
