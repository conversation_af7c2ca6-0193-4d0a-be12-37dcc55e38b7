using Api.Configuration;
using Api.Configuration.Options;
using Api.Extensions;
using Application;
using Application.AutomatedReading;
using Application.BlobStorage;
using Application.Report;
using Application.Sftp;
using Application.WebScraper;
using Azure.Monitor.OpenTelemetry.AspNetCore;
using Database;
using Elsa.Activities.Email.Options;
using Elsa.Retention.Options;
using Microsoft.AspNetCore.Mvc;
using Workflow;

var builder = WebApplication.CreateBuilder(new WebApplicationOptions
{
    ContentRootPath = Path.GetFullPath(Directory.GetCurrentDirectory()),
    WebRootPath = "wwwroot",
    Args = args
});

var localEnvironment = builder.Environment.IsEnvironment("Local");

builder.WebHost.UseContentRoot(Path.GetFullPath(Directory.GetCurrentDirectory()));
builder.WebHost.UseWebRoot("wwwroot");

if (localEnvironment)
{
    builder.WebHost.UseStaticWebAssets();
}
else
{
    builder.Configuration.ConfigureKeyVault();
}

var databaseOptions = builder.Configuration.GetSection(ConnectionStringOptions.Database).Get<ConnectionStringOptions>();
databaseOptions.ConnectionString = "Data Source=PAT-0189;Initial Catalog=sqldb-logisoil-workflows-dev;Integrated Security=True;Connect Timeout=30;Encrypt=False;Trust Server Certificate=False;Application Intent=ReadWrite;Multi Subnet Failover=False";

var messageBrokerOptions = builder.Configuration.GetSection(ConnectionStringOptions.MessageBroker).Get<ConnectionStringOptions>();
messageBrokerOptions.ConnectionString = "rabbitmq://guest:guest@localhost";

var smtpOptions = builder.Configuration.GetSection("Smtp").Get<SmtpOptions>();
var sftpOptions = builder.Configuration.GetSection(SftpOptions.Sftp).Get<SftpOptions>();
var azureMonitorOptions = builder.Configuration.GetSection("AzureMonitor").Get<AzureMonitorOptions>();
var cleanupOptions = builder.Configuration.GetSection("WorkflowCleanup").Get<CleanupOptions>();

builder.Services
    .AddOptions()
    .Configure<BlobStorageOptions>(builder.Configuration.GetSection(BlobStorageOptions.BlobStorage))
    .Configure<ScheduledReportOptions>(builder.Configuration.GetSection(ScheduledReportOptions.ScheduledReport))
    .Configure<WebScraperOptions>(builder.Configuration.GetSection(WebScraperOptions.WebScraper))
    .Configure<AutomatedReadingOptions>(builder.Configuration.GetSection(AutomatedReadingOptions.AutomatedReading));

builder.Configuration
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true)
    .AddEnvironmentVariables();

builder.Services.AddRazorPages();

builder.Services
    .AddTelemetry(azureMonitorOptions)
    .AddElsaApiEndpoints()
    .AddEndpointsApiExplorer()
    .AddSwaggerGen()
    .AddApiVersioning(options =>
    {
        options.AssumeDefaultVersionWhenUnspecified = true;
        options.DefaultApiVersion = new ApiVersion(1, 0);
        options.ReportApiVersions = true;
    })
    .AddLocalization()
    .AddWorkflows(databaseOptions.ConnectionString, smtpOptions, cleanupOptions)
    .AddMassTransit(messageBrokerOptions.ConnectionString)
    .AddApis(builder.Configuration)
    .AddEmailService(smtpOptions)
    .AddBlobStorageService()
    .AddDocxService()
    .AddWebScraper()
    .AddSftpService(sftpOptions)
    .AddResponseCompression()
    .AddControllers();

builder.Services
    .AddRepos(databaseOptions.ConnectionString)
    .AddFluentMigrator(databaseOptions.ConnectionString);

var app = builder.Build();

LocalizationCfg.ConfigureCulture("en-US");

app.UseStaticFiles();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    app.UseSwaggerUI();
}

app.UseRouting()
    .UseEndpoints(endpoints =>
    {
        endpoints.MapControllers();
        endpoints.MapFallbackToPage("/_Host");
    });

app.UseRequestLocalization();

app.UseHttpsRedirection();

app.UseAuthorization();

app.MapControllers();

app.Migrate(databaseOptions.ConnectionString);

await app.RunAsync();