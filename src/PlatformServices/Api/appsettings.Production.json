{"Apis": {"Keycloak": {"Url": "https://logisoil-prd.westus2.cloudapp.azure.com/auth", "Realm": "logisoil"}, "Clients": {"Url": "https://logisoil-prd.westus2.cloudapp.azure.com/clients-api"}, "Slide2": {"Url": "http://logisoil.com:51234"}, "Transcription": {"Url": "https://func-ivory-shared-prd.azurewebsites.net", "TimeoutInSeconds": 60}, "TextGeneration": {"Url": "https://func-ivory-shared-prd.azurewebsites.net", "TimeoutInSeconds": 60}}, "AutomatedReading": {"DeleteReadingsCronExpression": "0 0/30 * * * ?", "ImportReadingsCronExpression": "0 0/2 * * * ?", "SftpFilesPurgeDays": 30}, "BlobStorage": {"AccountName": "stwalmsharedprd", "ClientsContainer": "clients"}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Elsa": "Warning", "FluentMigrator": "Warning", "MassTransit": "Warning", "Quartz": "Warning", "Rebus": "Warning", "System.Net.Http.HttpClient.Refit.Implementation": "Warning"}, "Debug": {"LogLevel": {"Default": "Information", "Microsoft.Hosting": "Trace"}}, "EventSource": {"LogLevel": {"Default": "Warning"}}}, "ScheduledReport": {"CronExpression": "0 0/1 * * * ?", "HoursToLookBack": 1}, "Sftp": {"Host": "***************", "Port": 2022}, "Smtp": {"Host": "smtp.office365.com", "Port": 587, "DefaultSender": "<EMAIL>"}, "Vault": {"Url": "https://kv-walm-prd.vault.azure.net/", "TenantId": "f8f03f04-d496-4994-a0a7-f4f8e4d37148", "ClientId": "6d4c2a99-3ce1-4de4-8e22-ffe062c0cce5", "ClientSecret": "****************************************"}, "WebScraper": {"LogisoilFrontendUri": "https://zealous-beach-00853381e.4.azurestaticapps.net", "LogisoilAuthUri": "https://logisoil-prd.westus2.cloudapp.azure.com/auth/realms/logisoil/protocol/openid-connect/auth", "ViewportSize": {"Width": 1920, "Height": 1080}}, "WorkflowCleanup": {"TimeToLive": "03:00:00:00", "SweepInterval": "00:00:00:30", "BatchSize": 30}}