namespace Slide.Core.Objects.Sli
{
    public class Vertice
    {
        public int? Index { get; set; }
        public double X { get; set; }
        public double Y { get; set; }

        public const double ProximityTolerance = 0.001;

        public Vertice()
        {

        }

        public Vertice(double x, double y)
        {
            X = x;
            Y = y;
            Index = null;
        }

        public Vertice(Vertice vertice)
        {
            Index = vertice.Index;
            X = vertice.X;
            Y = vertice.Y;
        }

        public override string ToString()
        {
            return $"  {Index} x: {Math.Round(X, 12)}  y: {Math.Round(Y, 12)}";
        }

        /// <summary>
        /// Determines if this vertex is close to another vertex within a specified tolerance distance.
        /// Uses Euclidean distance calculation to compare the two vertices.
        /// </summary>
        /// <param name="other">The other vertex to compare against.</param>
        /// <returns>
        /// True if the distance between the vertices is less than or equal to the tolerance; otherwise, false.
        /// Returns false if the other vertex is null.
        /// </returns>
        public bool IsCloseTo(Vertice other)
        {
            if (other == null)
            {
                return false;
            }

            // Calculate Euclidean distance between the two vertices
            var distance = Math.Sqrt(Math.Pow(X - other.X, 2) + Math.Pow(Y - other.Y, 2));

            return distance <= ProximityTolerance);
        }
    }
}
