using System.Text;
using File = Application.Apis._Shared.File;

namespace Workflow.Tests.Section.Builders;

public static class DrawingBuilder
{
    private static File CreateDxf(string fileName = "1.dxf")
    {
        var currentDirectory = Directory.GetCurrentDirectory();
        var dxfPath = Path.Combine(
            currentDirectory,
            "Section",
            "Files",
            fileName);
        var dxf = System.IO.File.ReadAllText(dxfPath);
        var base64 = Convert.ToBase64String(Encoding.UTF8.GetBytes(dxf));
        return new File { Base64 = base64, Name = fileName };
    }
}

