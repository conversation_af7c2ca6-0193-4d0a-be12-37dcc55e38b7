using Bogus;
using FluentAssertions;
using Slide.Core.Objects.Sli;
using Workflow.Extensions;
using Xunit;

namespace Workflow.Tests.Extensions.VerticeExtensions;

[Trait("VerticeExtensions", "RemoveCloseVertices")]
public class RemoveCloseVerticesTests
{
    private readonly Faker _faker = new();
    private const double Tolerance = 0.001;

    [Fact(DisplayName = "When vertices list is null, should return empty list")]
    public void WhenVerticesListIsNull_ShouldReturnEmptyList()
    {
        List<Vertice> vertices = null;

        var result = vertices.RemoveCloseVertices();

        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact(DisplayName = "When vertices list is empty, should return empty list")]
    public void WhenVerticesListIsEmpty_ShouldReturnEmptyList()
    {
        var vertices = new List<Vertice>();

        var result = vertices.RemoveCloseVertices();

        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact(DisplayName = "When list contains only null vertices, should return empty list")]
    public void WhenListContainsOnlyNullVertices_ShouldReturnEmptyList()
    {
        var vertices = new List<Vertice> { null, null, null };

        var result = vertices.RemoveCloseVertices();

        result.Should().NotBeNull();
        result.Should().BeEmpty();
    }

    [Fact(DisplayName = "When list contains single vertex, should return that vertex")]
    public void WhenListContainsSingleVertex_ShouldReturnThatVertex()
    {
        var vertex = new Vertice(_faker.Random.Double(), _faker.Random.Double());
        var vertices = new List<Vertice> { vertex };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(1);
        result[0].Should().BeSameAs(vertex);
    }

    [Fact(DisplayName = "When vertices are identical, should keep only first occurrence")]
    public void WhenVerticesAreIdentical_ShouldKeepOnlyFirstOccurrence()
    {
        var x = _faker.Random.Double();
        var y = _faker.Random.Double();
        var vertex1 = new Vertice(x, y);
        var vertex2 = new Vertice(x, y);
        var vertex3 = new Vertice(x, y);
        var vertices = new List<Vertice> { vertex1, vertex2, vertex3 };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(1);
        result[0].Should().BeSameAs(vertex1);
    }

    [Fact(DisplayName = "When vertices are within tolerance, should keep only first occurrence")]
    public void WhenVerticesAreWithinTolerance_ShouldKeepOnlyFirstOccurrence()
    {
        var baseX = _faker.Random.Double();
        var baseY = _faker.Random.Double();
        var vertex1 = new Vertice(baseX, baseY);
        var vertex2 = new Vertice(baseX + Tolerance / 2, baseY + Tolerance / 2);
        var vertex3 = new Vertice(baseX - Tolerance / 2, baseY - Tolerance / 2);
        var vertices = new List<Vertice> { vertex1, vertex2, vertex3 };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(1);
        result[0].Should().BeSameAs(vertex1);
    }

    [Fact(DisplayName = "When vertices are outside tolerance, should keep all vertices")]
    public void WhenVerticesAreOutsideTolerance_ShouldKeepAllVertices()
    {
        var baseX = _faker.Random.Double();
        var baseY = _faker.Random.Double();
        var vertex1 = new Vertice(baseX, baseY);
        var vertex2 = new Vertice(baseX + Tolerance * 2, baseY);
        var vertex3 = new Vertice(baseX, baseY + Tolerance * 2);
        var vertices = new List<Vertice> { vertex1, vertex2, vertex3 };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(3);
        result.Should().Contain(vertex1);
        result.Should().Contain(vertex2);
        result.Should().Contain(vertex3);
    }

    [Fact(DisplayName = "When list contains mix of null and valid vertices, should filter out nulls")]
    public void WhenListContainsMixOfNullAndValidVertices_ShouldFilterOutNulls()
    {
        var vertex1 = new Vertice(_faker.Random.Double(), _faker.Random.Double());
        var vertex2 = new Vertice(_faker.Random.Double() + 10, _faker.Random.Double() + 10);
        var vertices = new List<Vertice> { vertex1, null, vertex2, null };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(2);
        result.Should().Contain(vertex1);
        result.Should().Contain(vertex2);
        result.Should().NotContain(v => v == null);
    }

    [Fact(DisplayName = "When vertices form chain of close pairs, should keep first of each group")]
    public void WhenVerticesFormChainOfClosePairs_ShouldKeepFirstOfEachGroup()
    {
        // Create vertices: A-B (close), C-D (close), where A-C are far apart
        var vertex1 = new Vertice(0, 0);
        var vertex2 = new Vertice(Tolerance / 2, 0); // Close to vertex1
        var vertex3 = new Vertice(10, 10); // Far from vertex1 and vertex2
        var vertex4 = new Vertice(10 + Tolerance / 2, 10); // Close to vertex3
        var vertices = new List<Vertice> { vertex1, vertex2, vertex3, vertex4 };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(2);
        result.Should().Contain(vertex1);
        result.Should().Contain(vertex3);
        result.Should().NotContain(vertex2);
        result.Should().NotContain(vertex4);
    }

    [Fact(DisplayName = "When vertices are exactly at tolerance boundary, should be considered close")]
    public void WhenVerticesAreExactlyAtToleranceBoundary_ShouldBeConsideredClose()
    {
        var vertex1 = new Vertice(0, 0);
        var vertex2 = new Vertice(Tolerance, 0); // Exactly at tolerance distance
        var vertices = new List<Vertice> { vertex1, vertex2 };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(1);
        result[0].Should().BeSameAs(vertex1);
    }

    [Fact(DisplayName = "When processing large list with no close vertices, should return all vertices")]
    public void WhenProcessingLargeListWithNoCloseVertices_ShouldReturnAllVertices()
    {
        var vertices = new List<Vertice>();
        for (int i = 0; i < 100; i++)
        {
            vertices.Add(new Vertice(i * 10, i * 10)); // Each vertex is far from others
        }

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(100);
        result.Should().BeEquivalentTo(vertices);
    }

    [Fact(DisplayName = "When vertices have same X but different Y outside tolerance, should keep both")]
    public void WhenVerticesHaveSameXButDifferentYOutsideTolerance_ShouldKeepBoth()
    {
        var x = _faker.Random.Double();
        var vertex1 = new Vertice(x, 0);
        var vertex2 = new Vertice(x, Tolerance * 2);
        var vertices = new List<Vertice> { vertex1, vertex2 };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(2);
        result.Should().Contain(vertex1);
        result.Should().Contain(vertex2);
    }

    [Fact(DisplayName = "When vertices have same Y but different X outside tolerance, should keep both")]
    public void WhenVerticesHaveSameYButDifferentXOutsideTolerance_ShouldKeepBoth()
    {
        var y = _faker.Random.Double();
        var vertex1 = new Vertice(0, y);
        var vertex2 = new Vertice(Tolerance * 2, y);
        var vertices = new List<Vertice> { vertex1, vertex2 };

        var result = vertices.RemoveCloseVertices();

        result.Should().HaveCount(2);
        result.Should().Contain(vertex1);
        result.Should().Contain(vertex2);
    }
}
