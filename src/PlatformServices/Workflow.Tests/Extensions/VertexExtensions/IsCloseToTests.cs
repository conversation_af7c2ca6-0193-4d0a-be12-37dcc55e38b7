using TriangleNet.Geometry;
using Workflow.Extensions;

namespace Workflow.Tests.Extensions.VertexExtensions;

[Trait("VertexExtensions", "IsCloseTo")]
public class IsCloseToTests
{
    private readonly Faker _faker = new();
    private const double Tolerance = 0.001;

    [Fact(DisplayName = "When other vertex is null, should return false")]
    public void WhenOtherVertexIsNull_ShouldReturnFalse()
    {
        var vertex = new Vertex(_faker.Random.Double(), _faker.Random.Double());

        var result = vertex.IsCloseTo(null);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices are exactly the same, should return true")]
    public void WhenVerticesAreExactlyTheSame_ShouldReturnTrue()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var y = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Vertex(x, y);
        var vertex2 = new Vertex(x, y);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are at exactly tolerance distance, should return true")]
    public void WhenVerticesAreAtExactlyToleranceDistance_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(0, 0);
        var vertex2 = new Vertex(Tolerance, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are within tolerance threshold, should return true")]
    public void WhenVerticesAreWithinToleranceThreshold_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(0, 0);
        var vertex2 = new Vertex(0.0005, 0.0005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are just outside tolerance threshold, should return false")]
    public void WhenVerticesAreJustOutsideToleranceThreshold_ShouldReturnFalse()
    {
        var vertex1 = new Vertex(0, 0);
        var vertex2 = new Vertex(0.002, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have zero coordinates, should return true")]
    public void WhenVerticesHaveZeroCoordinates_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(0, 0);
        var vertex2 = new Vertex(0, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have negative coordinates within tolerance, should return true")]
    public void WhenVerticesHaveNegativeCoordinatesWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(-10.5, -20.3);
        var vertex2 = new Vertex(-10.5005, -20.3005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have negative coordinates outside tolerance, should return false")]
    public void WhenVerticesHaveNegativeCoordinatesOutsideTolerance_ShouldReturnFalse()
    {
        var vertex1 = new Vertex(-10.5, -20.3);
        var vertex2 = new Vertex(-10.502, -20.3);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have large positive coordinates within tolerance, should return true")]
    public void WhenVerticesHaveLargePositiveCoordinatesWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(1000000.5, 2000000.3);
        var vertex2 = new Vertex(1000000.5005, 2000000.3005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have large positive coordinates outside tolerance, should return false")]
    public void WhenVerticesHaveLargePositiveCoordinatesOutsideTolerance_ShouldReturnFalse()
    {
        var vertex1 = new Vertex(1000000.5, 2000000.3);
        var vertex2 = new Vertex(1000000.502, 2000000.3);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When distance is calculated using Euclidean formula within tolerance, should return true")]
    public void WhenDistanceIsCalculatedUsingEuclideanFormulaWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(0, 0);
        // Distance = sqrt((0.0006)² + (0.0008)²) = sqrt(0.00000036 + 0.00000064) = sqrt(0.000001) = 0.001
        var vertex2 = new Vertex(0.0006, 0.0008);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When distance is calculated using Euclidean formula outside tolerance, should return false")]
    public void WhenDistanceIsCalculatedUsingEuclideanFormulaOutsideTolerance_ShouldReturnFalse()
    {
        var vertex1 = new Vertex(0, 0);
        // Distance = sqrt((0.0008)² + (0.0008)²) = sqrt(0.00000064 + 0.00000064) = sqrt(0.00000128) ≈ 0.00113 > 0.001
        var vertex2 = new Vertex(0.0008, 0.0008);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices differ only in X coordinate within tolerance, should return true")]
    public void WhenVerticesDifferOnlyInXCoordinateWithinTolerance_ShouldReturnTrue()
    {
        var y = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Vertex(100, y);
        var vertex2 = new Vertex(100.0005, y);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices differ only in Y coordinate within tolerance, should return true")]
    public void WhenVerticesDifferOnlyInYCoordinateWithinTolerance_ShouldReturnTrue()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Vertex(x, 200);
        var vertex2 = new Vertex(x, 200.0005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices differ only in X coordinate outside tolerance, should return false")]
    public void WhenVerticesDifferOnlyInXCoordinateOutsideTolerance_ShouldReturnFalse()
    {
        var y = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Vertex(100, y);
        var vertex2 = new Vertex(100.002, y);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices differ only in Y coordinate outside tolerance, should return false")]
    public void WhenVerticesDifferOnlyInYCoordinateOutsideTolerance_ShouldReturnFalse()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Vertex(x, 200);
        var vertex2 = new Vertex(x, 200.002);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have very small coordinate differences within tolerance, should return true")]
    public void WhenVerticesHaveVerySmallCoordinateDifferencesWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(0, 0);
        var vertex2 = new Vertex(0.0001, 0.0001);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When comparing vertex with itself, should return true")]
    public void WhenComparingVertexWithItself_ShouldReturnTrue()
    {
        var vertex = new Vertex(_faker.Random.Double(), _faker.Random.Double());

        var result = vertex.IsCloseTo(vertex);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have mixed positive and negative coordinates within tolerance, should return true")]
    public void WhenVerticesHaveMixedPositiveAndNegativeCoordinatesWithinTolerance_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(-5.5, 10.3);
        var vertex2 = new Vertex(-5.5005, 10.3005);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are at boundary of tolerance using random coordinates, should return true")]
    public void WhenVerticesAreAtBoundaryOfToleranceUsingRandomCoordinates_ShouldReturnTrue()
    {
        var x1 = _faker.Random.Double(-1000, 1000);
        var y1 = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Vertex(x1, y1);
        
        // Create a vertex exactly at tolerance distance
        var vertex2 = new Vertex(x1 + Tolerance, y1);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices are just beyond boundary of tolerance using random coordinates, should return false")]
    public void WhenVerticesAreJustBeyondBoundaryOfToleranceUsingRandomCoordinates_ShouldReturnFalse()
    {
        var x1 = _faker.Random.Double(-1000, 1000);
        var y1 = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Vertex(x1, y1);

        // Create a vertex just beyond tolerance distance
        var vertex2 = new Vertex(x1 + Tolerance + 0.0001, y1);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have ID property set, should still compare by coordinates only")]
    public void WhenVerticesHaveIdPropertySet_ShouldStillCompareByCoordinatesOnly()
    {
        var x = _faker.Random.Double(-1000, 1000);
        var y = _faker.Random.Double(-1000, 1000);
        var vertex1 = new Vertex(x, y, 1); // Different IDs
        var vertex2 = new Vertex(x, y, 2);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have different ID but coordinates outside tolerance, should return false")]
    public void WhenVerticesHaveDifferentIdButCoordinatesOutsideTolerance_ShouldReturnFalse()
    {
        var vertex1 = new Vertex(0, 0, 1);
        var vertex2 = new Vertex(0.002, 0, 2);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When using TriangleNet Vertex with default constructor, should handle correctly")]
    public void WhenUsingTriangleNetVertexWithDefaultConstructor_ShouldHandleCorrectly()
    {
        var vertex1 = new Vertex();
        var vertex2 = new Vertex();

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have extreme double values, should handle correctly")]
    public void WhenVerticesHaveExtremeDoubleValues_ShouldHandleCorrectly()
    {
        var vertex1 = new Vertex(double.MaxValue, double.MaxValue);
        var vertex2 = new Vertex(double.MaxValue, double.MaxValue);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have minimum double values, should handle correctly")]
    public void WhenVerticesHaveMinimumDoubleValues_ShouldHandleCorrectly()
    {
        var vertex1 = new Vertex(double.MinValue, double.MinValue);
        var vertex2 = new Vertex(double.MinValue, double.MinValue);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When vertices have NaN coordinates, should return false")]
    public void WhenVerticesHaveNaNCoordinates_ShouldReturnFalse()
    {
        var vertex1 = new Vertex(double.NaN, 0);
        var vertex2 = new Vertex(0, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When both vertices have NaN coordinates, should return false")]
    public void WhenBothVerticesHaveNaNCoordinates_ShouldReturnFalse()
    {
        var vertex1 = new Vertex(double.NaN, double.NaN);
        var vertex2 = new Vertex(double.NaN, double.NaN);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have positive and negative infinity, should return false")]
    public void WhenVerticesHavePositiveAndNegativeInfinity_ShouldReturnFalse()
    {
        var vertex1 = new Vertex(double.PositiveInfinity, 0);
        var vertex2 = new Vertex(double.NegativeInfinity, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have same infinity values, should return false due to NaN distance")]
    public void WhenVerticesHaveSameInfinityValues_ShouldReturnFalseDueToNaNDistance()
    {
        var vertex1 = new Vertex(double.PositiveInfinity, double.PositiveInfinity);
        var vertex2 = new Vertex(double.PositiveInfinity, double.PositiveInfinity);

        var result = vertex1.IsCloseTo(vertex2);

        // When calculating distance between infinity values, the result is NaN (infinity - infinity = NaN)
        // NaN comparisons always return false, so this should return false
        result.Should().BeFalse();
    }

    [Fact(DisplayName = "When vertices have very small epsilon differences, should return true")]
    public void WhenVerticesHaveVerySmallEpsilonDifferences_ShouldReturnTrue()
    {
        var vertex1 = new Vertex(0, 0);
        var vertex2 = new Vertex(double.Epsilon, double.Epsilon);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When tolerance constant matches expected value, should use correct tolerance")]
    public void WhenToleranceConstantMatchesExpectedValue_ShouldUseCorrectTolerance()
    {
        // This test verifies that the hardcoded tolerance in VertexExtensions matches our test constant
        var vertex1 = new Vertex(0, 0);
        var vertex2 = new Vertex(Tolerance, 0); // Should be exactly at the boundary

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue();
    }

    [Fact(DisplayName = "When distance calculation involves precision edge cases, should handle correctly")]
    public void WhenDistanceCalculationInvolvesPrecisionEdgeCases_ShouldHandleCorrectly()
    {
        // Test case where floating point precision might cause issues
        var vertex1 = new Vertex(0.1 + 0.2, 0); // 0.30000000000000004 due to floating point precision
        var vertex2 = new Vertex(0.3, 0);

        var result = vertex1.IsCloseTo(vertex2);

        result.Should().BeTrue(); // Should be within tolerance despite floating point precision issues
    }
}
